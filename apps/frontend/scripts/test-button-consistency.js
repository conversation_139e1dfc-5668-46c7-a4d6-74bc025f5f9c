/**
 * 按钮一致性测试脚本
 * 🎯 核心价值：验证所有按钮的视觉一致性和交互体验
 * 📦 功能范围：检查按钮样式、尺寸、颜色、交互效果
 * 🔄 架构设计：自动化测试脚本，确保设计系统的一致性
 */

// 测试按钮一致性的函数
function testButtonConsistency() {
  console.log('🔍 开始测试按钮一致性...');
  
  // 获取所有按钮元素
  const buttons = document.querySelectorAll('button');
  console.log(`📊 找到 ${buttons.length} 个按钮`);
  
  // 测试结果
  const results = {
    totalButtons: buttons.length,
    consistencyIssues: [],
    styleAnalysis: {},
    recommendations: []
  };
  
  // 分析每个按钮的样式
  buttons.forEach((button, index) => {
    const computedStyle = window.getComputedStyle(button);
    const buttonInfo = {
      index,
      element: button,
      borderRadius: computedStyle.borderRadius,
      boxShadow: computedStyle.boxShadow,
      fontSize: computedStyle.fontSize,
      fontWeight: computedStyle.fontWeight,
      padding: computedStyle.padding,
      transition: computedStyle.transition,
      backgroundColor: computedStyle.backgroundColor,
      color: computedStyle.color,
      border: computedStyle.border
    };
    
    // 检查是否使用了统一的Button组件
    const hasUnifiedClass = button.className.includes('inline-flex') && 
                           button.className.includes('rounded-lg') &&
                           button.className.includes('shadow-sm');
    
    if (!hasUnifiedClass) {
      results.consistencyIssues.push({
        buttonIndex: index,
        issue: '未使用统一的Button组件',
        element: button.outerHTML.substring(0, 100) + '...'
      });
    }
    
    results.styleAnalysis[index] = buttonInfo;
  });
  
  // 检查圆角一致性
  const borderRadiusValues = [...new Set(Object.values(results.styleAnalysis).map(b => b.borderRadius))];
  if (borderRadiusValues.length > 2) {
    results.consistencyIssues.push({
      issue: '圆角值不一致',
      values: borderRadiusValues
    });
  }
  
  // 检查阴影一致性
  const shadowValues = [...new Set(Object.values(results.styleAnalysis).map(b => b.boxShadow))];
  if (shadowValues.length > 3) {
    results.consistencyIssues.push({
      issue: '阴影效果不一致',
      values: shadowValues.length
    });
  }
  
  // 生成建议
  if (results.consistencyIssues.length === 0) {
    results.recommendations.push('✅ 所有按钮都使用了统一的设计系统');
  } else {
    results.recommendations.push('⚠️ 发现一致性问题，建议使用统一的Button组件');
  }
  
  // 输出结果
  console.log('📋 测试结果:', results);
  
  // 在页面上显示结果
  displayResults(results);
  
  return results;
}

// 在页面上显示测试结果
function displayResults(results) {
  // 创建结果显示容器
  const existingResults = document.getElementById('button-test-results');
  if (existingResults) {
    existingResults.remove();
  }
  
  const resultsContainer = document.createElement('div');
  resultsContainer.id = 'button-test-results';
  resultsContainer.style.cssText = `
    position: fixed;
    top: 20px;
    left: 20px;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-width: 400px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-size: 14px;
  `;
  
  resultsContainer.innerHTML = `
    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 12px;">
      <h3 style="margin: 0; color: #374151; font-size: 16px; font-weight: 600;">按钮一致性测试</h3>
      <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 18px; cursor: pointer; color: #6b7280;">×</button>
    </div>
    
    <div style="margin-bottom: 12px;">
      <strong>总按钮数:</strong> ${results.totalButtons}
    </div>
    
    <div style="margin-bottom: 12px;">
      <strong>一致性问题:</strong> ${results.consistencyIssues.length}
      ${results.consistencyIssues.length > 0 ? 
        '<ul style="margin: 8px 0; padding-left: 20px;">' + 
        results.consistencyIssues.map(issue => `<li style="color: #dc2626; margin: 4px 0;">${issue.issue}</li>`).join('') + 
        '</ul>' : 
        '<div style="color: #059669; margin: 8px 0;">✅ 无问题</div>'
      }
    </div>
    
    <div>
      <strong>建议:</strong>
      <ul style="margin: 8px 0; padding-left: 20px;">
        ${results.recommendations.map(rec => `<li style="margin: 4px 0;">${rec}</li>`).join('')}
      </ul>
    </div>
  `;
  
  document.body.appendChild(resultsContainer);
}

// 自动运行测试（延迟执行，确保页面加载完成）
setTimeout(() => {
  testButtonConsistency();
}, 2000);

// 导出测试函数供手动调用
window.testButtonConsistency = testButtonConsistency;
