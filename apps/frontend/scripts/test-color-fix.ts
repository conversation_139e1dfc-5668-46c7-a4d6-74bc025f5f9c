/**
 * 测试color字段修正逻辑
 * 验证color字段是否正确获取16进制数据并用于背景色设置
 */

import { DEFAULT_COLOR_VALUES } from '../core/data/GroupAData';
import type { BasicColorType } from '../core/matrix/MatrixTypes';

console.log('🎨 测试color字段修正逻辑\n');

// 测试所有颜色的16进制值获取
console.log('📋 颜色16进制值映射测试:');
const colors: BasicColorType[] = ['black', 'red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'];

colors.forEach(color => {
  const colorValue = DEFAULT_COLOR_VALUES[color];
  console.log(`  ${color.padEnd(8)} -> ${colorValue.hex} (${colorValue.name})`);
});

console.log('\n✅ 修正前后对比:');
console.log('修正前: 使用硬编码colorMap');
console.log('修正后: 使用DEFAULT_COLOR_VALUES[color].hex');

console.log('\n🔧 修正的好处:');
console.log('1. 统一颜色数据源，避免重复定义');
console.log('2. 确保颜色值的一致性');
console.log('3. 便于维护和修改颜色配置');
console.log('4. 支持动态颜色扩展');

// 模拟修正前后的背景色获取逻辑
console.log('\n🧪 逻辑对比测试:');

// 修正前的硬编码方式
const oldColorMap = {
  black: '#000000',
  red: '#ef4444',
  cyan: '#06b6d4',
  yellow: '#eab308',
  purple: '#a855f7',
  orange: '#f97316',
  green: '#22c55e',
  blue: '#3b82f6',
  pink: '#ec4899',
};

// 修正后的动态获取方式
const getBackgroundColorNew = (color: BasicColorType) => {
  return DEFAULT_COLOR_VALUES[color].hex;
};

const getBackgroundColorOld = (color: BasicColorType) => {
  return oldColorMap[color];
};

console.log('颜色值对比:');
colors.forEach(color => {
  const oldValue = getBackgroundColorOld(color);
  const newValue = getBackgroundColorNew(color);
  const isMatch = oldValue === newValue;
  console.log(`  ${color.padEnd(8)}: ${oldValue} -> ${newValue} ${isMatch ? '✅' : '❌'}`);
});

console.log('\n🎉 测试完成！color字段现在正确获取16进制数据用于背景色设置。');
