/**
 * 测试颜色模式下content为空的修改
 */

import { matrixCore } from '../core/matrix/MatrixCore';
import { createDefaultCell, DEFAULT_MATRIX_CONFIG } from '../core/matrix/MatrixTypes';
import type { MatrixData, CellData } from '../core/matrix/MatrixTypes';

console.log('🎨 测试颜色模式下content修改\n');

// 创建测试数据
const testCells = new Map<string, CellData>();

// 添加一些测试单元格
const testColors = ['red', 'blue', 'green', 'black'] as const;
testColors.forEach((color, index) => {
  const cell = createDefaultCell(index, 0);
  cell.color = color;
  testCells.set(`${index},0`, cell);
});

const testData: MatrixData = {
  cells: testCells,
  selectedCells: new Set(),
  hoveredCell: null,
  focusedCell: null,
};

// 测试颜色模式配置
const colorModeConfig = {
  ...DEFAULT_MATRIX_CONFIG,
  mode: 'color' as const,
};

console.log('📋 测试颜色模式下的content显示:');

// 处理数据
const processedData = matrixCore.processData(testData, colorModeConfig);

// 检查每个单元格的content
testColors.forEach((color, index) => {
  const key = `${index},0`;
  const renderData = processedData.renderData.get(key);
  
  if (renderData) {
    console.log(`  ${color.padEnd(8)}: content = "${renderData.content}" ${renderData.content === '' ? '✅' : '❌'}`);
    console.log(`    背景色: ${renderData.style.backgroundColor}`);
  } else {
    console.log(`  ${color.padEnd(8)}: 未找到渲染数据 ❌`);
  }
});

// 测试单个单元格渲染
console.log('\n🔧 测试单个单元格渲染:');
testColors.forEach((color, index) => {
  const cell = createDefaultCell(index, 0);
  cell.color = color;
  
  const renderData = matrixCore.renderCell(cell, colorModeConfig);
  console.log(`  ${color.padEnd(8)}: content = "${renderData.content}" ${renderData.content === '' ? '✅' : '❌'}`);
});

console.log('\n✅ 修改验证:');
console.log('- 颜色模式下所有格子的content现在都为空字符串');
console.log('- 格子只显示背景色，不显示任何文字内容');
console.log('- 修改同时影响了processData和renderCell方法');

console.log('\n🎉 测试完成！颜色模式下content已成功设置为空。');
