/**
 * 测试颜色模式下content修正
 */

import { useMatrixStore } from '../core/matrix/MatrixStore';

console.log('🎨 测试颜色模式下content修正\n');

// 获取store实例
const store = useMatrixStore.getState();

// 初始化矩阵
console.log('📋 初始化矩阵...');
store.initializeMatrix();

// 确保切换到颜色模式
console.log('🔄 切换到颜色模式...');
store.setMode('color');

// 获取当前配置确认模式
const currentConfig = store.config;
console.log(`当前模式: ${currentConfig.mode}`);

// 测试几个有颜色数据的单元格
console.log('\n🧪 测试颜色模式下的content显示:');

// 测试多个坐标点
const testCoordinates = [
  [16, 16], // 中心点
  [15, 16], // 中心附近
  [17, 16], // 中心附近
  [16, 15], // 中心附近
  [16, 17], // 中心附近
];

testCoordinates.forEach(([x, y]) => {
  const renderData = store.getCellRenderData(x, y);
  const hasMatrixData = store.hasMatrixData(x, y);
  const matrixData = store.getMatrixDataByCoordinate(x, y);
  
  if (renderData) {
    const contentStatus = renderData.content === '' ? '✅' : '❌';
    const bgColor = renderData.style.backgroundColor || '无';
    
    console.log(`  (${x},${y}): content="${renderData.content}" ${contentStatus}, 背景色=${bgColor}, 有数据=${hasMatrixData}`);
    
    if (matrixData) {
      console.log(`    数据: 颜色=${matrixData.color}, 组=${matrixData.group}, 级别=${matrixData.level}`);
    }
  } else {
    console.log(`  (${x},${y}): 无渲染数据 ❌`);
  }
});

// 测试模式切换对比
console.log('\n🔄 模式切换对比测试:');

const testX = 16, testY = 16;

// 颜色模式
store.setMode('color');
const colorModeData = store.getCellRenderData(testX, testY);
console.log(`颜色模式 (${testX},${testY}): content="${colorModeData?.content || 'null'}" ${(colorModeData?.content === '' || !colorModeData?.content) ? '✅' : '❌'}`);

// 坐标模式
store.setMode('coordinate');
const coordModeData = store.getCellRenderData(testX, testY);
console.log(`坐标模式 (${testX},${testY}): content="${coordModeData?.content || 'null'}" ${coordModeData?.content === `${testX},${testY}` ? '✅' : '❌'}`);

// 等级模式
store.setMode('level');
const levelModeData = store.getCellRenderData(testX, testY);
console.log(`等级模式 (${testX},${testY}): content="${levelModeData?.content || 'null'}" ${levelModeData?.content !== '' ? '✅' : '❌'}`);

// 回到颜色模式
store.setMode('color');
const finalColorModeData = store.getCellRenderData(testX, testY);
console.log(`颜色模式(最终) (${testX},${testY}): content="${finalColorModeData?.content || 'null'}" ${(finalColorModeData?.content === '' || !finalColorModeData?.content) ? '✅' : '❌'}`);

console.log('\n✅ 修正验证:');
console.log('- 颜色模式下content应该为空字符串');
console.log('- 其他模式下content应该有相应内容');
console.log('- 背景色应该正确显示颜色');

console.log('\n🎉 测试完成！');
