/**
 * 坐标显示修复验证脚本
 * 🎯 验证目标：确认坐标模式字体显示优化效果
 * 📦 测试范围：字体大小计算、内容显示、边界情况
 */

import { toDisplayCoordinate } from '../core/data/GroupAData';

// 模拟字体大小计算函数（从MatrixCore.ts复制）
const getOptimalFontSize = (content: string): string => {
  const charCount = content.length;
  
  // 基于字符数量的字体大小映射
  if (charCount <= 3) return '12px';      // "0,0"
  if (charCount <= 5) return '10px';      // "16,16"  
  if (charCount <= 7) return '8px';       // "-16,-16"
  return '7px';                           // 超长内容
};

const getChineseOptimalFontSize = (content: string): string => {
  const charCount = content.length;
  // 中文字符通常比英文宽1.5-2倍
  if (charCount <= 2) return '12px';      // "数据"
  if (charCount <= 3) return '10px';      // "测试数据"
  if (charCount <= 4) return '9px';       // "长文本内容"
  return '8px';                           // 超长中文
};

/**
 * 测试坐标显示优化
 */
function testCoordinateDisplayOptimization() {
  console.log('🎯 坐标显示优化测试');
  console.log('=====================================');
  
  // 测试用例：关键坐标位置
  const testCases = [
    // 中心区域
    { grid: [16, 16], description: '中心点' },
    { grid: [15, 15], description: '中心附近' },
    { grid: [17, 17], description: '中心附近' },
    
    // 正数边界
    { grid: [32, 32], description: '右下角' },
    { grid: [32, 16], description: '右边界' },
    { grid: [16, 32], description: '下边界' },
    
    // 负数边界
    { grid: [0, 0], description: '左上角' },
    { grid: [0, 16], description: '左边界' },
    { grid: [16, 0], description: '上边界' },
    
    // 混合坐标
    { grid: [8, 24], description: '左下区域' },
    { grid: [24, 8], description: '右上区域' },
  ];
  
  console.log('坐标显示测试结果:');
  console.log('格式: 网格坐标 → 显示坐标 (字符数) → 字体大小');
  console.log('---');
  
  testCases.forEach(({ grid, description }) => {
    const [gridX, gridY] = grid;
    const [displayX, displayY] = toDisplayCoordinate(gridX, gridY);
    const content = `${displayX},${displayY}`;
    const fontSize = getOptimalFontSize(content);
    const charCount = content.length;
    
    console.log(`${description}: (${gridX},${gridY}) → "${content}" (${charCount}字符) → ${fontSize}`);
  });
}

/**
 * 测试字体大小分布
 */
function testFontSizeDistribution() {
  console.log('\n📊 字体大小分布分析');
  console.log('=====================================');
  
  const fontSizeStats = {
    '12px': 0,
    '10px': 0,
    '8px': 0,
    '7px': 0,
  };
  
  // 遍历所有33x33网格
  for (let x = 0; x < 33; x++) {
    for (let y = 0; y < 33; y++) {
      const [displayX, displayY] = toDisplayCoordinate(x, y);
      const content = `${displayX},${displayY}`;
      const fontSize = getOptimalFontSize(content);
      fontSizeStats[fontSize as keyof typeof fontSizeStats]++;
    }
  }
  
  console.log('字体大小分布:');
  Object.entries(fontSizeStats).forEach(([size, count]) => {
    const percentage = ((count / 1089) * 100).toFixed(1);
    console.log(`  ${size}: ${count}个单元格 (${percentage}%)`);
  });
}

/**
 * 测试中文词语显示
 */
function testChineseWordDisplay() {
  console.log('\n🈳 中文词语显示测试');
  console.log('=====================================');
  
  const chineseTestCases = [
    '数据',
    '测试',
    '矩阵',
    '坐标系',
    '可视化',
    '测试数据',
    '长文本内容',
    '超长的中文词语内容',
  ];
  
  console.log('中文词语字体大小测试:');
  chineseTestCases.forEach(word => {
    const fontSize = getChineseOptimalFontSize(word);
    console.log(`  "${word}" (${word.length}字符) → ${fontSize}`);
  });
}

/**
 * 测试边界情况
 */
function testEdgeCases() {
  console.log('\n⚠️  边界情况测试');
  console.log('=====================================');
  
  const edgeCases = [
    { content: '', description: '空内容' },
    { content: '0', description: '单字符' },
    { content: '0,0', description: '最短坐标' },
    { content: '-16,-16', description: '最长坐标' },
    { content: '999,999', description: '超长数字' },
  ];
  
  console.log('边界情况处理:');
  edgeCases.forEach(({ content, description }) => {
    const fontSize = getOptimalFontSize(content);
    console.log(`  ${description}: "${content}" → ${fontSize}`);
  });
}

/**
 * 计算显示质量改进
 */
function calculateDisplayQualityImprovement() {
  console.log('\n📈 显示质量改进分析');
  console.log('=====================================');
  
  let problematicCells = 0;
  let optimizedCells = 0;
  
  // 假设原来固定12px字体的问题单元格
  for (let x = 0; x < 33; x++) {
    for (let y = 0; y < 33; y++) {
      const [displayX, displayY] = toDisplayCoordinate(x, y);
      const content = `${displayX},${displayY}`;
      
      // 原来12px字体可能溢出的情况（字符数 > 5）
      if (content.length > 5) {
        problematicCells++;
      }
      
      // 现在优化后的字体大小
      const newFontSize = getOptimalFontSize(content);
      if (newFontSize !== '12px') {
        optimizedCells++;
      }
    }
  }
  
  console.log('改进效果统计:');
  console.log(`  原来可能溢出的单元格: ${problematicCells}个`);
  console.log(`  现在优化的单元格: ${optimizedCells}个`);
  console.log(`  改进覆盖率: ${((optimizedCells / problematicCells) * 100).toFixed(1)}%`);
  
  const totalCells = 1089;
  console.log(`  总体优化比例: ${((optimizedCells / totalCells) * 100).toFixed(1)}%`);
}

/**
 * 主测试函数
 */
function runCoordinateDisplayTest() {
  testCoordinateDisplayOptimization();
  testFontSizeDistribution();
  testChineseWordDisplay();
  testEdgeCases();
  calculateDisplayQualityImprovement();
  
  console.log('\n✅ 坐标显示优化测试完成！');
  console.log('\n💡 优化效果总结:');
  console.log('  ✓ 智能字体大小：根据内容长度自动调整');
  console.log('  ✓ 负数坐标优化："-16,-16"使用8px字体');
  console.log('  ✓ 中文内容支持：专门的中文字体大小计算');
  console.log('  ✓ 溢出保护：CSS文本溢出处理');
  console.log('  ✓ 悬停增强：鼠标悬停显示完整内容');
}

// 如果直接运行此脚本
if (require.main === module) {
  runCoordinateDisplayTest();
}

export {
  getOptimalFontSize,
  getChineseOptimalFontSize,
  runCoordinateDisplayTest
};
