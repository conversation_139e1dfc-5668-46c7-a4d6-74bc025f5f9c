/**
 * 测试坐标修复脚本
 * 验证坐标模式是否正确显示以(0,0)为中心的坐标
 */

import { toDisplayCoordinate, GRID_CENTER } from '../core/data/GroupAData';

/**
 * 测试坐标转换函数
 */
function testCoordinateConversion() {
  console.log('🧪 坐标转换测试');
  console.log('=====================================');
  
  // 测试关键坐标点
  const testPoints = [
    { grid: [16, 16], expected: [0, 0], description: '中心点' },
    { grid: [0, 0], expected: [-16, -16], description: '左上角' },
    { grid: [32, 32], expected: [16, 16], description: '右下角' },
    { grid: [24, 16], expected: [8, 0], description: '中心右侧' },
    { grid: [8, 16], expected: [-8, 0], description: '中心左侧' },
    { grid: [16, 8], expected: [0, -8], description: '中心上方' },
    { grid: [16, 24], expected: [0, 8], description: '中心下方' },
  ];

  console.log(`网格中心点: (${GRID_CENTER.X}, ${GRID_CENTER.Y})`);
  console.log('\n坐标转换结果:');
  
  testPoints.forEach(({ grid, expected, description }) => {
    const [displayX, displayY] = toDisplayCoordinate(grid[0], grid[1]);
    const isCorrect = displayX === expected[0] && displayY === expected[1];
    const status = isCorrect ? '✅' : '❌';
    
    console.log(`${status} ${description}: 网格(${grid[0]},${grid[1]}) → 显示(${displayX},${displayY}) [期望(${expected[0]},${expected[1]})]`);
  });
}

/**
 * 模拟坐标模式内容生成
 */
function simulateCoordinateMode() {
  console.log('\n🎯 坐标模式内容生成测试');
  console.log('=====================================');
  
  // 模拟关键位置的单元格
  const testCells = [
    { x: 16, y: 16, description: '中心点' },
    { x: 24, y: 16, description: '红色L1位置' },
    { x: 8, y: 16, description: '青色L1位置' },
    { x: 16, y: 8, description: '黄色L1位置' },
    { x: 16, y: 24, description: '紫色L1位置' },
  ];

  console.log('坐标模式显示内容:');
  
  testCells.forEach(({ x, y, description }) => {
    const [displayX, displayY] = toDisplayCoordinate(x, y);
    const content = `${displayX},${displayY}`;
    
    console.log(`${description}: 网格(${x},${y}) → 显示内容"${content}"`);
  });
}

/**
 * 验证修复效果
 */
function verifyFix() {
  console.log('\n🔍 修复验证');
  console.log('=====================================');
  
  // 检查中心点是否显示 (0,0)
  const [centerDisplayX, centerDisplayY] = toDisplayCoordinate(16, 16);
  const centerContent = `${centerDisplayX},${centerDisplayY}`;
  
  if (centerContent === '0,0') {
    console.log('✅ 修复成功: 中心点正确显示 (0,0)');
  } else {
    console.log(`❌ 修复失败: 中心点显示 ${centerContent}，期望 (0,0)`);
  }
  
  // 检查其他关键点
  const [redDisplayX, redDisplayY] = toDisplayCoordinate(24, 16);
  const redContent = `${redDisplayX},${redDisplayY}`;
  
  if (redContent === '8,0') {
    console.log('✅ 红色L1位置正确显示 (8,0)');
  } else {
    console.log(`❌ 红色L1位置显示 ${redContent}，期望 (8,0)`);
  }
}

// 运行测试
if (require.main === module) {
  testCoordinateConversion();
  simulateCoordinateMode();
  verifyFix();
}

export {
  testCoordinateConversion,
  simulateCoordinateMode,
  verifyFix
};
