/**
 * 坐标系统测试脚本
 * 验证以(16,16)为中心点(0,0)的坐标模式
 */

import {
  fromDisplayCoordinate,
  getDataPointCoordinateInfo,
  GRID_CENTER,
  MatrixDataManager,
  toDisplayCoordinate,
  validateCoordinateSystem
} from '../core/data/GroupAData';

import { MATRIX_SIZE } from '../core/matrix/MatrixTypes';

/**
 * 测试坐标转换函数
 */
function testCoordinateConversion() {
  console.log('=== 坐标转换测试 ===');

  // 测试中心点
  const centerGrid = [GRID_CENTER.X, GRID_CENTER.Y];
  const centerDisplay = toDisplayCoordinate(centerGrid[0], centerGrid[1]);
  console.log(`中心点: 网格(${centerGrid[0]},${centerGrid[1]}) -> 显示(${centerDisplay[0]},${centerDisplay[1]})`);

  // 测试边界点
  const corners = [
    [0, 0],
    [MATRIX_SIZE - 1, 0],
    [0, MATRIX_SIZE - 1],
    [MATRIX_SIZE - 1, MATRIX_SIZE - 1]
  ];

  console.log('\n边界点转换:');
  corners.forEach(([x, y]) => {
    const display = toDisplayCoordinate(x, y);
    const backToGrid = fromDisplayCoordinate(display[0], display[1]);
    console.log(`网格(${x},${y}) -> 显示(${display[0]},${display[1]}) -> 网格(${backToGrid[0]},${backToGrid[1]})`);
  });

  // 测试一些特殊点
  console.log('\n特殊点转换:');
  const specialPoints = [
    [8, 8],   // 左上象限
    [24, 8],  // 右上象限
    [8, 24],  // 左下象限
    [24, 24]  // 右下象限
  ];

  specialPoints.forEach(([x, y]) => {
    const display = toDisplayCoordinate(x, y);
    console.log(`网格(${x},${y}) -> 显示(${display[0]},${display[1]})`);
  });
}

/**
 * 测试坐标系统验证
 */
function testCoordinateSystemValidation() {
  console.log('\n=== 坐标系统验证 ===');

  const validation = validateCoordinateSystem();
  console.log(`验证结果: ${validation.isValid ? '✅ 通过' : '❌ 失败'}`);
  console.log(`中心点网格坐标: (${validation.centerGrid[0]},${validation.centerGrid[1]})`);
  console.log(`中心点显示坐标: (${validation.centerDisplay[0]},${validation.centerDisplay[1]})`);

  console.log('\n坐标范围:');
  console.log(`网格坐标: (${validation.ranges.grid.min[0]},${validation.ranges.grid.min[1]}) 到 (${validation.ranges.grid.max[0]},${validation.ranges.grid.max[1]})`);
  console.log(`显示坐标: (${validation.ranges.display.min[0]},${validation.ranges.display.min[1]}) 到 (${validation.ranges.display.max[0]},${validation.ranges.display.max[1]})`);

  console.log('\n验证信息:');
  validation.messages.forEach(msg => console.log(`  ${msg}`));
}

/**
 * 测试A组数据的坐标信息
 */
function testGroupADataCoordinates() {
  console.log('\n=== A组数据坐标信息 ===');

  const groupAData = MatrixDataManager.getGroupAData();
  const samplePoints = groupAData.points.slice(0, 10); // 取前10个点作为示例

  console.log('示例数据点坐标信息:');
  samplePoints.forEach(point => {
    const coordInfo = getDataPointCoordinateInfo(point);
    console.log(`${point.color} L${point.level}: 网格${coordInfo.grid.formatted} -> 显示${coordInfo.display.formatted} ${coordInfo.isCenter ? '(中心点)' : ''}`);
  });

  // 查找中心点的数据
  const centerPoint = MatrixDataManager.getByDisplayCoordinate(groupAData, 0, 0);
  if (centerPoint) {
    console.log(`\n中心点(0,0)的数据: ${centerPoint.color} L${centerPoint.level} ID:${centerPoint.id}`);
  } else {
    console.log('\n中心点(0,0)没有数据');
  }
}

/**
 * 测试显示坐标查询
 */
function testDisplayCoordinateQuery() {
  console.log('\n=== 显示坐标查询测试 ===');

  const groupAData = MatrixDataManager.getGroupAData();

  // 测试一些显示坐标的查询
  const testCoords = [
    [0, 0],   // 中心点
    [8, 0],   // 右侧
    [-8, 0],  // 左侧
    [0, 8],   // 下方
    [0, -8],  // 上方
    [4, 2],   // 右下
    [-4, -2]  // 左上
  ];

  console.log('显示坐标查询结果:');
  testCoords.forEach(([x, y]) => {
    const point = MatrixDataManager.getByDisplayCoordinate(groupAData, x, y);
    if (point) {
      console.log(`显示(${x},${y}): ${point.color} L${point.level} ID:${point.id}`);
    } else {
      console.log(`显示(${x},${y}): 无数据`);
    }
  });
}

/**
 * 主测试函数
 */
function runCoordinateSystemTests() {
  console.log('🎯 坐标系统测试开始');
  console.log(`矩阵大小: ${MATRIX_SIZE}×${MATRIX_SIZE}`);
  console.log(`网格中心: (${GRID_CENTER.X},${GRID_CENTER.Y})`);
  console.log('=====================================');

  try {
    testCoordinateConversion();
    testCoordinateSystemValidation();
    testGroupADataCoordinates();
    testDisplayCoordinateQuery();

    console.log('\n🎉 所有测试完成！');
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runCoordinateSystemTests();
}

export {
  runCoordinateSystemTests,
  testCoordinateConversion,
  testCoordinateSystemValidation, testDisplayCoordinateQuery, testGroupADataCoordinates
};

