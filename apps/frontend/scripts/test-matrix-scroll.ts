/**
 * 矩阵滚动功能测试脚本
 * 🎯 测试目标：验证30px格子尺寸和滚动功能
 * 📦 测试范围：格子尺寸、滚动容器、响应式行为
 */

import { MATRIX_SIZE } from '@/core/matrix/MatrixTypes';

// 测试配置
const TEST_CONFIG = {
  CELL_SIZE: 30,
  CELL_GAP: 1,
  CELL_UNIT: 31, // 30px + 1px gap
  EXPECTED_MATRIX_SIZE: 33 * 31, // 1023px
};

/**
 * 测试矩阵尺寸计算
 */
function testMatrixSizeCalculation() {
  console.log('🧮 测试矩阵尺寸计算...');
  
  const actualMatrixSize = MATRIX_SIZE * TEST_CONFIG.CELL_UNIT;
  const expectedSize = TEST_CONFIG.EXPECTED_MATRIX_SIZE;
  
  console.log(`矩阵大小: ${MATRIX_SIZE} x ${MATRIX_SIZE}`);
  console.log(`格子尺寸: ${TEST_CONFIG.CELL_SIZE}px x ${TEST_CONFIG.CELL_SIZE}px`);
  console.log(`格子间距: ${TEST_CONFIG.CELL_GAP}px`);
  console.log(`单元总尺寸: ${TEST_CONFIG.CELL_UNIT}px`);
  console.log(`计算的矩阵总尺寸: ${actualMatrixSize}px`);
  console.log(`期望的矩阵总尺寸: ${expectedSize}px`);
  
  if (actualMatrixSize === expectedSize) {
    console.log('✅ 矩阵尺寸计算正确');
    return true;
  } else {
    console.log('❌ 矩阵尺寸计算错误');
    return false;
  }
}

/**
 * 测试DOM元素尺寸
 */
function testDOMElementSizes() {
  console.log('\n🔍 测试DOM元素尺寸...');
  
  // 等待DOM加载
  if (typeof window === 'undefined') {
    console.log('⚠️ 此测试需要在浏览器环境中运行');
    return false;
  }
  
  const viewport = document.querySelector('.matrix-viewport') as HTMLElement;
  const container = document.querySelector('.matrix-container') as HTMLElement;
  const cells = document.querySelectorAll('.matrix-cell');
  
  if (!viewport || !container) {
    console.log('❌ 未找到矩阵容器元素');
    return false;
  }
  
  console.log(`视口元素: ${viewport.className}`);
  console.log(`容器元素: ${container.className}`);
  console.log(`容器尺寸: ${container.style.width} x ${container.style.height}`);
  console.log(`格子数量: ${cells.length}`);
  
  // 检查第一个格子的尺寸
  if (cells.length > 0) {
    const firstCell = cells[0] as HTMLElement;
    const computedStyle = window.getComputedStyle(firstCell);
    console.log(`第一个格子尺寸: ${computedStyle.width} x ${computedStyle.height}`);
    
    const expectedCellSize = `${TEST_CONFIG.CELL_SIZE}px`;
    if (computedStyle.width === expectedCellSize && computedStyle.height === expectedCellSize) {
      console.log('✅ 格子尺寸正确');
      return true;
    } else {
      console.log(`❌ 格子尺寸错误，期望: ${expectedCellSize}`);
      return false;
    }
  }
  
  return false;
}

/**
 * 测试滚动功能
 */
function testScrollFunctionality() {
  console.log('\n📜 测试滚动功能...');
  
  if (typeof window === 'undefined') {
    console.log('⚠️ 此测试需要在浏览器环境中运行');
    return false;
  }
  
  const viewport = document.querySelector('.matrix-viewport') as HTMLElement;
  
  if (!viewport) {
    console.log('❌ 未找到视口元素');
    return false;
  }
  
  // 检查滚动属性
  const computedStyle = window.getComputedStyle(viewport);
  console.log(`视口overflow: ${computedStyle.overflow}`);
  console.log(`视口scrollable width: ${viewport.scrollWidth}px`);
  console.log(`视口scrollable height: ${viewport.scrollHeight}px`);
  console.log(`视口client width: ${viewport.clientWidth}px`);
  console.log(`视口client height: ${viewport.clientHeight}px`);
  
  const isScrollable = viewport.scrollWidth > viewport.clientWidth || 
                      viewport.scrollHeight > viewport.clientHeight;
  
  if (isScrollable) {
    console.log('✅ 视口可滚动');
    
    // 测试滚动到不同位置
    console.log('🔄 测试滚动位置...');
    
    // 滚动到中心
    const centerX = (viewport.scrollWidth - viewport.clientWidth) / 2;
    const centerY = (viewport.scrollHeight - viewport.clientHeight) / 2;
    viewport.scrollTo(centerX, centerY);
    
    setTimeout(() => {
      console.log(`滚动到中心: (${viewport.scrollLeft}, ${viewport.scrollTop})`);
      
      // 滚动到右下角
      viewport.scrollTo(viewport.scrollWidth, viewport.scrollHeight);
      
      setTimeout(() => {
        console.log(`滚动到右下角: (${viewport.scrollLeft}, ${viewport.scrollTop})`);
        
        // 滚动回原点
        viewport.scrollTo(0, 0);
        console.log('✅ 滚动功能测试完成');
      }, 100);
    }, 100);
    
    return true;
  } else {
    console.log('❌ 视口不可滚动');
    return false;
  }
}

/**
 * 测试响应式行为
 */
function testResponsiveBehavior() {
  console.log('\n📱 测试响应式行为...');
  
  if (typeof window === 'undefined') {
    console.log('⚠️ 此测试需要在浏览器环境中运行');
    return false;
  }
  
  const viewport = document.querySelector('.matrix-viewport') as HTMLElement;
  
  if (!viewport) {
    console.log('❌ 未找到视口元素');
    return false;
  }
  
  const computedStyle = window.getComputedStyle(viewport);
  console.log(`视口最大宽度: ${computedStyle.maxWidth}`);
  console.log(`视口最大高度: ${computedStyle.maxHeight}`);
  console.log(`当前视口尺寸: ${viewport.clientWidth}px x ${viewport.clientHeight}px`);
  console.log(`窗口尺寸: ${window.innerWidth}px x ${window.innerHeight}px`);
  
  // 检查是否保持正方形比例
  const aspectRatio = viewport.clientWidth / viewport.clientHeight;
  console.log(`视口宽高比: ${aspectRatio.toFixed(2)}`);
  
  if (Math.abs(aspectRatio - 1) < 0.1) {
    console.log('✅ 视口保持正方形比例');
    return true;
  } else {
    console.log('⚠️ 视口未保持正方形比例');
    return false;
  }
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('🚀 开始矩阵滚动功能测试...\n');
  
  const results = [
    testMatrixSizeCalculation(),
    testDOMElementSizes(),
    testScrollFunctionality(),
    testResponsiveBehavior(),
  ];
  
  const passedTests = results.filter(Boolean).length;
  const totalTests = results.length;
  
  console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！矩阵滚动功能正常');
  } else {
    console.log('⚠️ 部分测试失败，请检查实现');
  }
  
  return passedTests === totalTests;
}

// 导出测试函数
export {
  testMatrixSizeCalculation,
  testDOMElementSizes,
  testScrollFunctionality,
  testResponsiveBehavior,
  runAllTests,
};

// 如果在浏览器环境中直接运行
if (typeof window !== 'undefined') {
  // 等待DOM加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(runAllTests, 1000); // 延迟1秒确保组件渲染完成
    });
  } else {
    setTimeout(runAllTests, 1000);
  }
}
