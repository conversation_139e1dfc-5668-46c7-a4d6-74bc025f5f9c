/**
 * 测试MatrixStore中的content修改
 */

import { useMatrixStore } from '../core/matrix/MatrixStore';
import { createDefaultCell } from '../core/matrix/MatrixTypes';
import type { MatrixConfig } from '../core/matrix/MatrixTypes';

console.log('🎨 测试MatrixStore中颜色模式content修改\n');

// 获取store实例
const store = useMatrixStore.getState();

// 创建测试配置
const colorModeConfig: MatrixConfig = {
  mode: 'color',
};

// 创建测试单元格
const testCell = createDefaultCell(5, 5);
testCell.color = 'red';

console.log('📋 测试getCellRenderData方法:');

// 先初始化矩阵以确保有数据
store.initializeMatrix();

// 测试getCellRenderData
const renderData = store.getCellRenderData(5, 5);

if (renderData) {
  console.log(`  坐标(5,5): content = "${renderData.content}" ${renderData.content === '' ? '✅' : '❌'}`);
  console.log(`  背景色: ${renderData.style.backgroundColor}`);
  console.log(`  类名: ${renderData.className}`);
} else {
  console.log('  未找到渲染数据 ❌');
}

// 测试不同颜色的单元格
console.log('\n🔧 测试不同颜色单元格:');
const testColors = ['red', 'blue', 'green', 'yellow'] as const;

testColors.forEach((color, index) => {
  const x = index;
  const y = 0;
  
  // 设置单元格颜色
  store.updateCell(x, y, { color });
  
  // 获取渲染数据
  const cellRenderData = store.getCellRenderData(x, y);
  
  if (cellRenderData) {
    console.log(`  ${color.padEnd(8)}: content = "${cellRenderData.content}" ${cellRenderData.content === '' ? '✅' : '❌'}`);
  } else {
    console.log(`  ${color.padEnd(8)}: 未找到渲染数据 ❌`);
  }
});

// 测试模式切换
console.log('\n🔄 测试模式切换:');

// 切换到颜色模式
store.setMode('color');
const colorModeRenderData = store.getCellRenderData(0, 0);
console.log(`颜色模式: content = "${colorModeRenderData?.content || 'null'}" ${(colorModeRenderData?.content === '' || !colorModeRenderData?.content) ? '✅' : '❌'}`);

// 切换到坐标模式验证对比
store.setMode('coordinate');
const coordModeRenderData = store.getCellRenderData(0, 0);
console.log(`坐标模式: content = "${coordModeRenderData?.content || 'null'}" ${coordModeRenderData?.content === '0,0' ? '✅' : '❌'}`);

// 切换回颜色模式
store.setMode('color');
const finalColorModeRenderData = store.getCellRenderData(0, 0);
console.log(`颜色模式(再次): content = "${finalColorModeRenderData?.content || 'null'}" ${(finalColorModeRenderData?.content === '' || !finalColorModeRenderData?.content) ? '✅' : '❌'}`);

console.log('\n✅ 修改验证:');
console.log('- MatrixStore.computeCellContent在颜色模式下返回空字符串');
console.log('- getCellRenderData方法正确使用computeCellContent');
console.log('- 颜色模式下格子不显示任何文字内容');

console.log('\n🎉 测试完成！MatrixStore中的颜色模式content已修正。');
