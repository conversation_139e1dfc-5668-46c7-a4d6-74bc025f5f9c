#!/usr/bin/env tsx

/**
 * 测试优化后的GroupAData模块
 * 验证所有功能是否正常工作
 */

import { MatrixDataManager } from '../core/data/GroupAData';

console.log('🧪 测试优化后的GroupAData模块...\n');

// 测试1: 基础数据生成
console.log('📊 测试1: 基础数据生成');
const groupAData = MatrixDataManager.generateGroupData('A');
console.log(`✅ A组数据点数量: ${groupAData.length}`);

// 测试2: 数据集创建
console.log('\n📊 测试2: 数据集创建');
const dataSet = MatrixDataManager.createDataSet(['A', 'B']);
console.log(`✅ 数据集总点数: ${dataSet.metadata.totalPoints}`);
console.log(`✅ 颜色分组数: ${dataSet.byColor.size}`);
console.log(`✅ 级别分组数: ${dataSet.byLevel.size}`);

// 测试3: 数据查询
console.log('\n📊 测试3: 数据查询');
const redPoints = MatrixDataManager.getByColor(dataSet, 'red');
console.log(`✅ 红色数据点数量: ${redPoints.length}`);

const level1Points = MatrixDataManager.getByLevel(dataSet, 1);
console.log(`✅ 级别1数据点数量: ${level1Points.length}`);

// 测试4: 坐标查询
console.log('\n📊 测试4: 坐标查询');
const centerPoint = MatrixDataManager.getByCoordinate(dataSet, 16, 16);
if (centerPoint) {
  console.log(`✅ 中心点数据: ${centerPoint.color} 级别${centerPoint.level} 组${centerPoint.group}`);
} else {
  console.log('ℹ️  中心点无数据');
}

// 测试5: 数据验证
console.log('\n📊 测试5: 数据验证');
const validation = MatrixDataManager.validate(dataSet);
console.log(`✅ 验证结果: ${validation.isValid ? '通过' : '失败'}`);
console.log(`✅ 错误数量: ${validation.errors.length}`);
console.log(`✅ 警告数量: ${validation.warnings.length}`);

// 测试6: 统计信息
console.log('\n📊 测试6: 统计信息');
const stats = MatrixDataManager.getStatistics(dataSet);
console.log(`✅ 网格利用率: ${(stats.densityAnalysis.gridUtilization * 100).toFixed(2)}%`);
console.log(`✅ 活跃组数: ${stats.densityAnalysis.activeGroups}`);
console.log(`✅ 平均每组数据点: ${stats.densityAnalysis.averagePointsPerGroup}`);

// 测试7: 缓存功能
console.log('\n📊 测试7: 缓存功能');
const cachedData1 = MatrixDataManager.getGroupAData();
const cachedData2 = MatrixDataManager.getGroupAData();
console.log(`✅ 缓存一致性: ${cachedData1 === cachedData2 ? '通过' : '失败'}`);

// 测试8: 组完整性检查
console.log('\n📊 测试8: 组完整性检查');
const completeness = MatrixDataManager.checkCompleteness('A');
console.log(`✅ A组完整性: ${completeness.completeness.toFixed(2)}%`);
console.log(`✅ 期望点数: ${completeness.expectedPoints}`);
console.log(`✅ 实际点数: ${completeness.actualPoints}`);

// 测试9: 映射值功能
console.log('\n📊 测试9: 映射值功能');
const blackPoints = MatrixDataManager.getByColor(dataSet, 'black');
if (blackPoints.length > 0) {
  const blackPoint = blackPoints[0];
  const mappingValue = MatrixDataManager.getMappingValue(blackPoint);
  console.log(`✅ 黑色点映射值: ${mappingValue} (应该是组字符)`);
}

if (redPoints.length > 0) {
  const redPoint = redPoints[0];
  const redMappingValue = MatrixDataManager.getMappingValue(redPoint);
  console.log(`✅ 红色点映射值: ${redMappingValue} (应该是数字1)`);
}

// 测试颜色映射配置
const blackColorMapping = MatrixDataManager.getColorMapping('black');
const redColorMapping = MatrixDataManager.getColorMapping('red');
console.log(`✅ 黑色颜色映射: ${blackColorMapping} (应该是'group')`);
console.log(`✅ 红色颜色映射: ${redColorMapping} (应该是1)`);

console.log('\n🎉 所有测试完成！');
