/**
 * 验证重构后的架构是否正常工作
 */

import { useMatrixStore } from '../core/matrix/MatrixStore';
import { matrixCore } from '../core/matrix/MatrixCore';

console.log('🔧 验证重构后的架构\n');

// 获取store实例
const store = useMatrixStore.getState();

// 初始化矩阵
console.log('📋 初始化矩阵...');
store.initializeMatrix();

console.log('🎯 测试统一的业务逻辑调用:');

// 测试坐标
const testX = 16, testY = 16;

// 1. 直接调用MatrixCore
const cell = store.data.cells.get(`${testX},${testY}`);
if (cell) {
  console.log('\n🔧 直接调用MatrixCore:');
  
  // 测试不同模式
  const modes = ['coordinate', 'color', 'level', 'word'] as const;
  
  modes.forEach(mode => {
    const config = { mode };
    const renderData = matrixCore.renderCell(cell, config);
    console.log(`  ${mode.padEnd(10)}: content="${renderData.content}", bg=${renderData.style.backgroundColor || '无'}`);
  });
}

// 2. 通过MatrixStore调用（应该调用相同的MatrixCore逻辑）
console.log('\n🏪 通过MatrixStore调用:');

const modes = ['coordinate', 'color', 'level', 'word'] as const;

modes.forEach(mode => {
  store.setMode(mode);
  const renderData = store.getCellRenderData(testX, testY);
  if (renderData) {
    console.log(`  ${mode.padEnd(10)}: content="${renderData.content}", bg=${renderData.style.backgroundColor || '无'}`);
  }
});

// 3. 验证结果一致性
console.log('\n✅ 验证结果一致性:');

if (cell) {
  let allConsistent = true;
  
  modes.forEach(mode => {
    const config = { mode };
    
    // 直接调用MatrixCore
    const coreResult = matrixCore.renderCell(cell, config);
    
    // 通过Store调用
    store.setMode(mode);
    const storeResult = store.getCellRenderData(testX, testY);
    
    if (storeResult) {
      const contentMatch = coreResult.content === storeResult.content;
      const bgMatch = coreResult.style.backgroundColor === storeResult.style.backgroundColor;
      const consistent = contentMatch && bgMatch;
      
      console.log(`  ${mode.padEnd(10)}: ${consistent ? '✅' : '❌'} (content: ${contentMatch ? '✅' : '❌'}, bg: ${bgMatch ? '✅' : '❌'})`);
      
      if (!consistent) {
        allConsistent = false;
        console.log(`    Core: content="${coreResult.content}", bg="${coreResult.style.backgroundColor}"`);
        console.log(`    Store: content="${storeResult.content}", bg="${storeResult.style.backgroundColor}"`);
      }
    } else {
      console.log(`  ${mode.padEnd(10)}: ❌ Store返回null`);
      allConsistent = false;
    }
  });
  
  console.log(`\n🎉 重构${allConsistent ? '成功' : '失败'}！${allConsistent ? '所有调用都使用统一的MatrixCore逻辑。' : '存在不一致的结果。'}`);
} else {
  console.log('❌ 未找到测试单元格');
}

console.log('\n📊 架构优化总结:');
console.log('- ✅ 删除了MatrixStore中的冗余业务逻辑函数');
console.log('- ✅ getCellRenderData现在统一调用MatrixCore.renderCell');
console.log('- ✅ 实现了清晰的分层架构：MatrixCore(业务) -> MatrixStore(状态) -> Matrix(视图)');
console.log('- ✅ 消除了代码重复，提高了维护性');

console.log('\n🎯 测试完成！');
