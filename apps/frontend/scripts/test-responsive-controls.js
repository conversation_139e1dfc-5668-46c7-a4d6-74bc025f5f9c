/**
 * 响应式控制面板测试脚本
 * 🎯 核心价值：验证控制按钮尺寸统一和响应式自动隐藏功能
 * 📦 功能范围：按钮尺寸检查、响应式行为测试、用户体验验证
 * 🔄 架构设计：自动化测试脚本，确保响应式设计的正确性
 */

// 测试配置
const TEST_CONFIG = {
  MATRIX_CELL_SIZE: 33, // 矩阵格子尺寸
  AUTO_HIDE_BREAKPOINT: 1474, // 自动隐藏临界点 (1122 + 320 + 32)
  BUTTON_TOLERANCE: 2, // 尺寸容差
};

// 测试响应式控制面板功能
function testResponsiveControls() {
  console.log('🔍 开始测试响应式控制面板...');
  
  const results = {
    buttonSizeTest: false,
    responsiveTest: false,
    autoHideTest: false,
    userExperienceTest: false,
    issues: [],
    recommendations: []
  };
  
  // 测试1: 控制按钮尺寸统一性
  console.log('📏 测试控制按钮尺寸统一性...');
  const menuButton = document.querySelector('[title="显示控制面板"]');
  const closeButton = document.querySelector('[title="隐藏控制面板"]');
  
  if (menuButton) {
    const menuStyle = window.getComputedStyle(menuButton);
    const menuWidth = parseInt(menuStyle.width);
    const menuHeight = parseInt(menuStyle.height);
    
    console.log(`菜单按钮尺寸: ${menuWidth}px x ${menuHeight}px`);
    console.log(`期望尺寸: ${TEST_CONFIG.MATRIX_CELL_SIZE}px x ${TEST_CONFIG.MATRIX_CELL_SIZE}px`);
    
    const widthMatch = Math.abs(menuWidth - TEST_CONFIG.MATRIX_CELL_SIZE) <= TEST_CONFIG.BUTTON_TOLERANCE;
    const heightMatch = Math.abs(menuHeight - TEST_CONFIG.MATRIX_CELL_SIZE) <= TEST_CONFIG.BUTTON_TOLERANCE;
    
    if (widthMatch && heightMatch) {
      console.log('✅ 菜单按钮尺寸与格子一致');
      results.buttonSizeTest = true;
    } else {
      console.log('❌ 菜单按钮尺寸与格子不一致');
      results.issues.push('菜单按钮尺寸与矩阵格子不匹配');
    }
  } else {
    console.log('⚠️ 未找到菜单按钮（可能控制面板当前可见）');
  }
  
  if (closeButton) {
    const closeStyle = window.getComputedStyle(closeButton);
    const closeWidth = parseInt(closeStyle.width);
    const closeHeight = parseInt(closeStyle.height);
    
    console.log(`关闭按钮尺寸: ${closeWidth}px x ${closeHeight}px`);
    
    const widthMatch = Math.abs(closeWidth - TEST_CONFIG.MATRIX_CELL_SIZE) <= TEST_CONFIG.BUTTON_TOLERANCE;
    const heightMatch = Math.abs(closeHeight - TEST_CONFIG.MATRIX_CELL_SIZE) <= TEST_CONFIG.BUTTON_TOLERANCE;
    
    if (widthMatch && heightMatch) {
      console.log('✅ 关闭按钮尺寸与格子一致');
    } else {
      console.log('❌ 关闭按钮尺寸与格子不一致');
      results.issues.push('关闭按钮尺寸与矩阵格子不匹配');
    }
  }
  
  // 测试2: 响应式行为
  console.log('📱 测试响应式行为...');
  const currentWidth = window.innerWidth;
  const controlsPanel = document.querySelector('.controls-sidebar');
  
  console.log(`当前窗口宽度: ${currentWidth}px`);
  console.log(`自动隐藏临界点: ${TEST_CONFIG.AUTO_HIDE_BREAKPOINT}px`);
  
  if (currentWidth < TEST_CONFIG.AUTO_HIDE_BREAKPOINT) {
    if (!controlsPanel || controlsPanel.style.display === 'none') {
      console.log('✅ 窗口较小时控制面板正确隐藏');
      results.responsiveTest = true;
    } else {
      console.log('❌ 窗口较小时控制面板未隐藏');
      results.issues.push('响应式隐藏逻辑未正确工作');
    }
  } else {
    if (controlsPanel && controlsPanel.style.display !== 'none') {
      console.log('✅ 窗口较大时控制面板正确显示');
      results.responsiveTest = true;
    } else {
      console.log('⚠️ 窗口较大但控制面板未显示（可能被用户手动隐藏）');
    }
  }
  
  // 测试3: 自动隐藏功能
  console.log('🔄 测试自动隐藏功能...');
  
  // 模拟窗口缩放测试
  const originalWidth = window.innerWidth;
  
  // 检查是否有遮罩层（旧的移动端方案）
  const overlay = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-50');
  if (!overlay) {
    console.log('✅ 已移除移动端遮罩层，使用响应式方案');
    results.autoHideTest = true;
  } else {
    console.log('❌ 仍存在移动端遮罩层');
    results.issues.push('未完全移除移动端遮罩方案');
  }
  
  // 测试4: 用户体验
  console.log('👤 测试用户体验...');
  
  // 检查按钮的可访问性
  const buttons = document.querySelectorAll('button[title]');
  let accessibilityScore = 0;
  
  buttons.forEach(button => {
    const title = button.getAttribute('title');
    const hasAriaLabel = button.hasAttribute('aria-label');
    const hasTitle = title && title.length > 0;
    
    if (hasTitle || hasAriaLabel) {
      accessibilityScore++;
    }
  });
  
  if (accessibilityScore === buttons.length) {
    console.log('✅ 所有按钮都有适当的可访问性标签');
    results.userExperienceTest = true;
  } else {
    console.log('⚠️ 部分按钮缺少可访问性标签');
    results.issues.push('部分按钮缺少可访问性标签');
  }
  
  // 生成建议
  if (results.issues.length === 0) {
    results.recommendations.push('✅ 响应式控制面板功能完美实现');
    results.recommendations.push('✅ 按钮尺寸与矩阵格子完全统一');
    results.recommendations.push('✅ 用户体验流畅无感');
  } else {
    results.recommendations.push('⚠️ 发现一些需要优化的问题');
    results.issues.forEach(issue => {
      results.recommendations.push(`🔧 修复: ${issue}`);
    });
  }
  
  // 输出结果
  console.log('📋 测试结果:', results);
  displayTestResults(results);
  
  return results;
}

// 在页面上显示测试结果
function displayTestResults(results) {
  const existingResults = document.getElementById('responsive-test-results');
  if (existingResults) {
    existingResults.remove();
  }
  
  const resultsContainer = document.createElement('div');
  resultsContainer.id = 'responsive-test-results';
  resultsContainer.style.cssText = `
    position: fixed;
    top: 20px;
    left: 20px;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-width: 450px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-size: 14px;
    max-height: 80vh;
    overflow-y: auto;
  `;
  
  const passedTests = Object.values(results).filter(v => v === true).length;
  const totalTests = 4;
  
  resultsContainer.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
      <h3 style="margin: 0; color: #374151; font-size: 16px; font-weight: 600;">响应式控制面板测试</h3>
      <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 18px; cursor: pointer; color: #6b7280;">×</button>
    </div>
    
    <div style="margin-bottom: 12px;">
      <strong>测试通过率:</strong> ${passedTests}/${totalTests} (${Math.round(passedTests/totalTests*100)}%)
    </div>
    
    <div style="margin-bottom: 12px;">
      <strong>按钮尺寸统一:</strong> ${results.buttonSizeTest ? '✅ 通过' : '❌ 失败'}
    </div>
    
    <div style="margin-bottom: 12px;">
      <strong>响应式行为:</strong> ${results.responsiveTest ? '✅ 通过' : '❌ 失败'}
    </div>
    
    <div style="margin-bottom: 12px;">
      <strong>自动隐藏功能:</strong> ${results.autoHideTest ? '✅ 通过' : '❌ 失败'}
    </div>
    
    <div style="margin-bottom: 12px;">
      <strong>用户体验:</strong> ${results.userExperienceTest ? '✅ 通过' : '❌ 失败'}
    </div>
    
    ${results.issues.length > 0 ? `
    <div style="margin-bottom: 12px;">
      <strong style="color: #dc2626;">发现问题:</strong>
      <ul style="margin: 8px 0; padding-left: 20px;">
        ${results.issues.map(issue => `<li style="color: #dc2626; margin: 4px 0;">${issue}</li>`).join('')}
      </ul>
    </div>
    ` : ''}
    
    <div>
      <strong>建议:</strong>
      <ul style="margin: 8px 0; padding-left: 20px;">
        ${results.recommendations.map(rec => `<li style="margin: 4px 0;">${rec}</li>`).join('')}
      </ul>
    </div>
  `;
  
  document.body.appendChild(resultsContainer);
}

// 窗口缩放测试函数
function testWindowResize() {
  console.log('🔄 开始窗口缩放测试...');
  
  const originalWidth = window.innerWidth;
  console.log(`原始窗口宽度: ${originalWidth}px`);
  
  // 模拟窗口缩小
  console.log('模拟窗口缩小到1200px...');
  // 注意：实际的窗口缩放需要用户手动操作，这里只是提示
  console.log('请手动缩小浏览器窗口到1200px以下，观察控制面板是否自动隐藏');
  console.log('然后再放大窗口，观察控制面板是否保持隐藏状态（需要点击菜单按钮显示）');
}

// 自动运行测试
setTimeout(() => {
  testResponsiveControls();
}, 2000);

// 导出测试函数
window.testResponsiveControls = testResponsiveControls;
window.testWindowResize = testWindowResize;
