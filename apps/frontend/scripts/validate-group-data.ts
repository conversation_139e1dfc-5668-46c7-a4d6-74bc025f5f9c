/**
 * A-M组数据验证脚本
 * 🎯 验证所有组的数据完整性和渲染状态
 */

import {
  checkGroupCompleteness,
  generateHeatmapData,
  getCompleteMatrixData,
  getMatrixDataStatistics,
  validateMatrixDataSet
} from '../core/data/GroupAData';

// 本地类型定义
type GroupType = 'A' | 'B' | 'C' | 'D' | 'E' | 'F' | 'G' | 'H' | 'I' | 'J' | 'K' | 'L' | 'M';

// 所有组类型
const ALL_GROUPS: GroupType[] = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M'];

console.log('🚀 开始验证 A-M 组数据完整性...\n');

// 1. 验证完整数据集
console.log('📊 验证完整数据集:');
const completeData = getCompleteMatrixData();
const validation = validateMatrixDataSet(completeData);
console.log(`✅ 数据有效性: ${validation.isValid ? '通过' : '失败'}`);

if (validation.errors.length > 0) {
  console.log('❌ 错误:');
  validation.errors.forEach(error => console.log(`  - ${error}`));
}

if (validation.warnings.length > 0) {
  console.log('⚠️  警告:');
  validation.warnings.forEach(warning => console.log(`  - ${warning}`));
}

// 2. 获取统计信息
console.log('\n📈 数据统计信息:');
const stats = getMatrixDataStatistics(completeData);
console.log(`总数据点: ${stats.totalPoints}`);
console.log(`网格利用率: ${stats.densityAnalysis.gridUtilization}`);
console.log(`平均每组数据点: ${stats.densityAnalysis.averagePointsPerGroup}`);

console.log('\n按组统计:');
Object.entries(stats.groupCounts).forEach(([group, count]) => {
  if (count > 0) {
    console.log(`  ${group}组: ${count} 个数据点`);
  }
});

console.log('\n按颜色统计:');
Object.entries(stats.colorCounts).forEach(([color, count]) => {
  if (count > 0) {
    console.log(`  ${color}: ${count} 个数据点`);
  }
});

console.log('\n按级别统计:');
Object.entries(stats.levelCounts).forEach(([level, count]) => {
  if (count > 0) {
    console.log(`  级别${level}: ${count} 个数据点`);
  }
});

// 3. 检查每个组的完整性
console.log('\n🔍 检查各组完整性:');
ALL_GROUPS.forEach(group => {
  const completeness = checkGroupCompleteness(group);
  const status = completeness.completeness === 100 ? '✅' : '⚠️';
  console.log(`${status} ${group}组: ${completeness.actualPoints}/${completeness.expectedPoints} (${completeness.completeness.toFixed(1)}%)`);

  if (completeness.missingData.length > 0) {
    console.log(`    缺失数据: ${completeness.missingData.join(', ')}`);
  }
});

// 4. 生成热力图数据概览
console.log('\n🔥 数据分布热力图概览:');
const heatmap = generateHeatmapData(completeData);
const maxDensity = Math.max(...heatmap.flat());
const minDensity = Math.min(...heatmap.flat().filter(v => v > 0));
console.log(`最大密度: ${maxDensity} 个数据点/格子`);
console.log(`最小密度: ${minDensity} 个数据点/格子`);

// 5. 坐标范围检查
console.log('\n📍 坐标范围:');
console.log(`X轴: ${stats.coordinateRange.xMin} ~ ${stats.coordinateRange.xMax}`);
console.log(`Y轴: ${stats.coordinateRange.yMin} ~ ${stats.coordinateRange.yMax}`);

// 6. 总结
console.log('\n📋 验证总结:');
if (validation.isValid && validation.warnings.length === 0) {
  console.log('🎉 所有 A-M 组数据完全渲染成功！');
} else if (validation.isValid) {
  console.log('✅ A-M 组数据基本完整，但有一些警告需要注意');
} else {
  console.log('❌ A-M 组数据存在问题，需要修复');
}

console.log(`\n📊 最终统计: ${stats.totalPoints} 个数据点分布在 ${Object.keys(stats.groupCounts).filter(g => stats.groupCounts[g as GroupType] > 0).length} 个组中`);
