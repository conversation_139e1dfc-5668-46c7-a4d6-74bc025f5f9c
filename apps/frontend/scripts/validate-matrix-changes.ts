/**
 * 验证矩阵修改脚本
 * 🎯 验证目标：确认30px格子和滚动功能的实现
 * 📦 验证范围：代码修改、样式更新、功能完整性
 */

import fs from 'fs';
import path from 'path';

// 验证配置
const VALIDATION_CONFIG = {
  EXPECTED_CELL_SIZE: '30px',
  EXPECTED_CELL_UNIT: 31, // 30px + 1px gap
  EXPECTED_MATRIX_SIZE: 1023, // 33 * 31
  EXPECTED_FONT_SIZE: '12px',
};

/**
 * 验证Matrix.tsx文件的修改
 */
function validateMatrixComponent() {
  console.log('🔍 验证Matrix.tsx组件修改...');
  
  const matrixPath = path.join(process.cwd(), 'apps/frontend/components/Matrix.tsx');
  
  if (!fs.existsSync(matrixPath)) {
    console.log('❌ Matrix.tsx文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(matrixPath, 'utf-8');
  
  // 检查关键修改点
  const checks = [
    {
      name: '格子尺寸修改为30px',
      pattern: /width:\s*['"]30px['"]/,
      found: content.includes("width: '30px'")
    },
    {
      name: '格子高度修改为30px',
      pattern: /height:\s*['"]30px['"]/,
      found: content.includes("height: '30px'")
    },
    {
      name: '位置计算使用31px单元',
      pattern: /\$\{[xy]\s*\*\s*31\}px/,
      found: content.includes('${x * 31}px') && content.includes('${y * 31}px')
    },
    {
      name: '字体大小调整为12px',
      pattern: /fontSize:\s*['"]12px['"]/,
      found: content.includes("fontSize: '12px'")
    },
    {
      name: '添加了matrix-viewport类',
      pattern: /matrix-viewport/,
      found: content.includes('matrix-viewport')
    },
    {
      name: '添加了滚动视口样式',
      pattern: /viewportStyle/,
      found: content.includes('viewportStyle')
    },
    {
      name: '矩阵实际大小常量',
      pattern: /MATRIX_ACTUAL_SIZE/,
      found: content.includes('MATRIX_ACTUAL_SIZE')
    }
  ];
  
  let passedChecks = 0;
  
  checks.forEach(check => {
    if (check.found) {
      console.log(`✅ ${check.name}`);
      passedChecks++;
    } else {
      console.log(`❌ ${check.name}`);
    }
  });
  
  console.log(`Matrix.tsx验证结果: ${passedChecks}/${checks.length} 通过\n`);
  return passedChecks === checks.length;
}

/**
 * 验证globals.css文件的修改
 */
function validateGlobalStyles() {
  console.log('🎨 验证globals.css样式修改...');
  
  const stylesPath = path.join(process.cwd(), 'apps/frontend/styles/globals.css');
  
  if (!fs.existsSync(stylesPath)) {
    console.log('❌ globals.css文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(stylesPath, 'utf-8');
  
  // 检查样式修改
  const checks = [
    {
      name: '添加了matrix-viewport样式',
      found: content.includes('.matrix-viewport')
    },
    {
      name: '添加了滚动条样式',
      found: content.includes('::-webkit-scrollbar')
    },
    {
      name: '更新了字体大小',
      found: content.includes('font-size: 11px') || content.includes('font-size: 12px')
    },
    {
      name: '添加了悬停阴影效果',
      found: content.includes('box-shadow: 0 2px 8px')
    },
    {
      name: '保留了矩阵容器样式',
      found: content.includes('.matrix-container')
    }
  ];
  
  let passedChecks = 0;
  
  checks.forEach(check => {
    if (check.found) {
      console.log(`✅ ${check.name}`);
      passedChecks++;
    } else {
      console.log(`❌ ${check.name}`);
    }
  });
  
  console.log(`globals.css验证结果: ${passedChecks}/${checks.length} 通过\n`);
  return passedChecks === checks.length;
}

/**
 * 验证page.tsx文件的修改
 */
function validatePageComponent() {
  console.log('📄 验证page.tsx页面修改...');
  
  const pagePath = path.join(process.cwd(), 'apps/frontend/app/page.tsx');
  
  if (!fs.existsSync(pagePath)) {
    console.log('❌ page.tsx文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(pagePath, 'utf-8');
  
  // 检查页面修改
  const checks = [
    {
      name: '移除了重复的矩阵样式',
      found: !content.includes('.matrix-cell {') || content.split('.matrix-cell {').length <= 2
    },
    {
      name: '保留了响应式样式',
      found: content.includes('@media (max-width:')
    },
    {
      name: '保留了Matrix组件引用',
      found: content.includes('<Matrix')
    }
  ];
  
  let passedChecks = 0;
  
  checks.forEach(check => {
    if (check.found) {
      console.log(`✅ ${check.name}`);
      passedChecks++;
    } else {
      console.log(`❌ ${check.name}`);
    }
  });
  
  console.log(`page.tsx验证结果: ${passedChecks}/${checks.length} 通过\n`);
  return passedChecks === checks.length;
}

/**
 * 验证测试文件的创建
 */
function validateTestFiles() {
  console.log('🧪 验证测试文件创建...');
  
  const testFiles = [
    'apps/frontend/scripts/test-matrix-scroll.ts',
    'apps/frontend/public/test-matrix-scroll.html'
  ];
  
  let existingFiles = 0;
  
  testFiles.forEach(filePath => {
    const fullPath = path.join(process.cwd(), filePath);
    if (fs.existsSync(fullPath)) {
      console.log(`✅ ${filePath} 存在`);
      existingFiles++;
    } else {
      console.log(`❌ ${filePath} 不存在`);
    }
  });
  
  console.log(`测试文件验证结果: ${existingFiles}/${testFiles.length} 通过\n`);
  return existingFiles === testFiles.length;
}

/**
 * 验证计算逻辑
 */
function validateCalculations() {
  console.log('🧮 验证计算逻辑...');
  
  const MATRIX_SIZE = 33;
  const CELL_SIZE = 30;
  const CELL_GAP = 1;
  const CELL_UNIT = CELL_SIZE + CELL_GAP;
  const MATRIX_ACTUAL_SIZE = MATRIX_SIZE * CELL_UNIT;
  
  console.log(`矩阵大小: ${MATRIX_SIZE} x ${MATRIX_SIZE}`);
  console.log(`格子尺寸: ${CELL_SIZE}px x ${CELL_SIZE}px`);
  console.log(`格子间距: ${CELL_GAP}px`);
  console.log(`单元总尺寸: ${CELL_UNIT}px`);
  console.log(`矩阵总尺寸: ${MATRIX_ACTUAL_SIZE}px`);
  
  const checks = [
    {
      name: '单元尺寸计算正确',
      expected: VALIDATION_CONFIG.EXPECTED_CELL_UNIT,
      actual: CELL_UNIT,
      passed: CELL_UNIT === VALIDATION_CONFIG.EXPECTED_CELL_UNIT
    },
    {
      name: '矩阵总尺寸计算正确',
      expected: VALIDATION_CONFIG.EXPECTED_MATRIX_SIZE,
      actual: MATRIX_ACTUAL_SIZE,
      passed: MATRIX_ACTUAL_SIZE === VALIDATION_CONFIG.EXPECTED_MATRIX_SIZE
    }
  ];
  
  let passedChecks = 0;
  
  checks.forEach(check => {
    if (check.passed) {
      console.log(`✅ ${check.name}: ${check.actual}`);
      passedChecks++;
    } else {
      console.log(`❌ ${check.name}: 期望 ${check.expected}, 实际 ${check.actual}`);
    }
  });
  
  console.log(`计算逻辑验证结果: ${passedChecks}/${checks.length} 通过\n`);
  return passedChecks === checks.length;
}

/**
 * 运行所有验证
 */
function runAllValidations() {
  console.log('🚀 开始验证矩阵修改...\n');
  
  const results = [
    validateMatrixComponent(),
    validateGlobalStyles(),
    validatePageComponent(),
    validateTestFiles(),
    validateCalculations()
  ];
  
  const passedValidations = results.filter(Boolean).length;
  const totalValidations = results.length;
  
  console.log(`📊 验证结果: ${passedValidations}/${totalValidations} 通过`);
  
  if (passedValidations === totalValidations) {
    console.log('🎉 所有验证通过！矩阵修改实施成功');
    console.log('\n✨ 修改总结:');
    console.log('• 格子尺寸从20px增加到30px');
    console.log('• 格子间距从2px减少到1px');
    console.log('• 矩阵总尺寸从726px增加到1023px');
    console.log('• 添加了滚动视口容器');
    console.log('• 优化了滚动条样式');
    console.log('• 保持了正方形比例');
    console.log('• 创建了测试文件');
  } else {
    console.log('⚠️ 部分验证失败，请检查实现');
  }
  
  return passedValidations === totalValidations;
}

// 导出验证函数
export {
  validateMatrixComponent,
  validateGlobalStyles,
  validatePageComponent,
  validateTestFiles,
  validateCalculations,
  runAllValidations
};

// 如果直接运行此脚本
if (require.main === module) {
  runAllValidations();
}
