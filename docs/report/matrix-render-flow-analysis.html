<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>矩阵格子完整渲染路径和调度流程分析</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 700;
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .section {
            background: white;
            margin: 30px 0;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-left: 5px solid #667eea;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.8em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 30px;
            font-size: 1.4em;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }
        
        .mermaid-container {
            background: #fafbfc;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .mermaid {
            text-align: center;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .feature-card h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 1.2em;
        }
        
        .feature-card p {
            margin: 0;
            color: #6c757d;
        }
        
        .summary-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin: 30px 0;
        }
        
        .summary-box h3 {
            margin-top: 0;
            color: white;
            border-bottom: 2px solid rgba(255,255,255,0.3);
        }
        
        .summary-list {
            list-style: none;
            padding: 0;
        }
        
        .summary-list li {
            padding: 8px 0;
            padding-left: 25px;
            position: relative;
        }
        
        .summary-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #fff;
            font-weight: bold;
        }
        
        .toc {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .toc h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        
        .toc li {
            padding: 5px 0;
        }
        
        .toc a {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
        
        .timestamp {
            text-align: center;
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 40px;
            padding: 20px;
            border-top: 1px solid #dee2e6;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .section {
                padding: 20px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 矩阵格子完整渲染路径和调度流程分析</h1>
        <p>Cube1 Group 项目 - 矩阵系统架构深度解析</p>
    </div>

    <div class="toc">
        <h3>📋 目录</h3>
        <ul>
            <li><a href="#overview">1. 系统概览</a></li>
            <li><a href="#architecture">2. 完整架构流程图</a></li>
            <li><a href="#sequence">3. 函数调用序列图</a></li>
            <li><a href="#data-structure">4. 状态和数据结构图</a></li>
            <li><a href="#key-features">5. 关键特性</a></li>
            <li><a href="#summary">6. 总结</a></li>
        </ul>
    </div>

    <section id="overview" class="section">
        <h2>🔍 系统概览</h2>
        <p>本文档详细分析了Cube1 Group项目中矩阵格子系统的完整渲染路径和调度流程。该系统基于React + TypeScript + Zustand构建，实现了33x33矩阵的高性能渲染和多模式切换功能。</p>
        
        <div class="features-grid">
            <div class="feature-card">
                <h4>🗄️ 数据驱动</h4>
                <p>基于OPTIMIZED_GROUP_A_DATA的预处理数据集，支持9种颜色和4个层级的矩阵数据</p>
            </div>
            <div class="feature-card">
                <h4>🔄 模式切换</h4>
                <p>支持coordinate、color、value、word四种显示模式的无缝切换</p>
            </div>
            <div class="feature-card">
                <h4>⚡ 高性能</h4>
                <p>使用Map/Set数据结构和计算属性缓存，确保1089个单元格的流畅渲染</p>
            </div>
            <div class="feature-card">
                <h4>📊 性能监控</h4>
                <p>完整的性能指标收集和警告机制，实时监控渲染性能</p>
            </div>
        </div>
    </section>

    <section id="architecture" class="section">
        <h2>🏗️ 完整架构流程图</h2>
        <p>以下流程图展示了矩阵系统从数据源到最终渲染的完整架构，包括数据源层、状态管理层、核心引擎层、组件层以及各种业务流程。</p>
        
        <div class="mermaid-container">
            <div class="mermaid">
graph TB
    %% 数据源层
    subgraph DataSource["🗄️ 数据源层"]
        GroupAData["GROUP_A_DATA<br/>基础数据结构"]
        OptimizedData["OPTIMIZED_GROUP_A_DATA<br/>预处理数据集"]
        DataUtils["数据工具函数<br/>• coordinateKey()<br/>• toAbsoluteCoordinate()<br/>• applyOffset()"]
        
        GroupAData --> OptimizedData
        DataUtils --> OptimizedData
    end

    %% 状态管理层
    subgraph StateLayer["🏪 状态管理层"]
        ZustandStore["useMatrixStore<br/>(Zustand + Persist)"]
        MatrixState["MatrixStoreState<br/>• data: MatrixData<br/>• config: MatrixConfig<br/>• cache: ComputedCache<br/>• metrics: PerformanceMetrics"]
        StoreActions["MatrixStoreActions<br/>• initializeMatrix()<br/>• setMode()<br/>• selectCell()<br/>• updateCell()"]
        
        ZustandStore --> MatrixState
        ZustandStore --> StoreActions
    end

    %% 核心引擎层
    subgraph CoreEngine["⚙️ 核心引擎层"]
        MatrixCore["MatrixCore 类"]
        ModeHandlers["业务模式处理器<br/>• coordinateModeHandler<br/>• colorModeHandler<br/>• valueModeHandler<br/>• wordModeHandler"]
        ProcessData["processData()<br/>数据处理管道"]
        RenderCell["renderCell()<br/>单元格渲染"]
        HandleInteraction["handleInteraction()<br/>交互处理"]
        
        MatrixCore --> ModeHandlers
        MatrixCore --> ProcessData
        MatrixCore --> RenderCell
        MatrixCore --> HandleInteraction
    end

    %% 组件层
    subgraph ComponentLayer["🎨 组件层"]
        MatrixComponent["Matrix 组件"]
        MatrixHooks["Matrix Hooks<br/>• useMatrixData()<br/>• useMatrixConfig()<br/>• useMatrixStore()"]
        RenderLogic["渲染逻辑<br/>• renderMatrixCells()<br/>• 事件处理器"]
        
        MatrixComponent --> MatrixHooks
        MatrixComponent --> RenderLogic
    end

    %% 初始化流程
    subgraph InitFlow["🚀 初始化流程"]
        Start["组件挂载"]
        CheckClient["检查客户端渲染"]
        CheckInit["检查是否已初始化"]
        InitMatrix["initializeMatrix()"]
        CreateCells["创建33x33单元格"]
        LoadData["加载OPTIMIZED_GROUP_A_DATA"]
        SetCellData["设置单元格数据<br/>• color: BasicColorType<br/>• level: DataLevel<br/>• value: number"]
        CacheUpdate["更新缓存"]
        
        Start --> CheckClient
        CheckClient --> CheckInit
        CheckInit --> InitMatrix
        InitMatrix --> CreateCells
        CreateCells --> LoadData
        LoadData --> SetCellData
        SetCellData --> CacheUpdate
    end

    %% 模式切换流程
    subgraph ModeSwitch["🔄 模式切换流程"]
        TriggerMode["触发模式切换<br/>setMode(mode)"]
        UpdateConfig["更新配置<br/>config.mode = mode"]
        InvalidateCache["invalidateCache()"]
        SelectHandler["选择模式处理器<br/>modeHandlers[mode]"]
        ProcessModeData["processData(data, config)"]
        GenerateRenderData["生成渲染数据<br/>Map<string, CellRenderData>"]
        
        TriggerMode --> UpdateConfig
        UpdateConfig --> InvalidateCache
        InvalidateCache --> SelectHandler
        SelectHandler --> ProcessModeData
        ProcessModeData --> GenerateRenderData
    end

    %% 渲染流程
    subgraph RenderFlow["🎯 渲染流程"]
        RenderTrigger["渲染触发"]
        GetRenderData["getCellRenderData(x, y)"]
        ComputeStyle["计算样式<br/>• backgroundColor<br/>• border<br/>• color<br/>• fontSize"]
        ComputeContent["计算内容<br/>• coordinate: x,y<br/>• color: color name<br/>• value: level<br/>• word: word"]
        ComputeClassName["计算CSS类名<br/>• matrix-cell<br/>• mode-specific<br/>• state classes"]
        CreateElement["创建DOM元素"]
        
        RenderTrigger --> GetRenderData
        GetRenderData --> ComputeStyle
        GetRenderData --> ComputeContent
        GetRenderData --> ComputeClassName
        ComputeStyle --> CreateElement
        ComputeContent --> CreateElement
        ComputeClassName --> CreateElement
    end

    %% 交互流程
    subgraph InteractionFlow["🖱️ 交互流程"]
        UserAction["用户交互<br/>• click<br/>• hover<br/>• focus"]
        ParseCoordinate["解析坐标<br/>parseCoordinateKey()"]
        UpdateState["更新状态<br/>• selectCell()<br/>• hoverCell()<br/>• focusCell()"]
        TriggerRerender["触发重渲染"]
        
        UserAction --> ParseCoordinate
        ParseCoordinate --> UpdateState
        UpdateState --> TriggerRerender
    end

    %% 性能监控
    subgraph Performance["📊 性能监控"]
        StartTracking["startPerformanceTracking()"]
        Metrics["性能指标<br/>• renderTime<br/>• updateTime<br/>• cacheHitRate<br/>• memoryUsage"]
        EndTracking["endPerformanceTracking()"]
        
        StartTracking --> Metrics
        Metrics --> EndTracking
    end

    %% 连接关系
    OptimizedData --> ZustandStore
    StoreActions --> MatrixCore
    MatrixCore --> MatrixComponent
    
    InitMatrix --> CreateCells
    CreateCells --> OptimizedData
    
    TriggerMode --> ModeHandlers
    GenerateRenderData --> RenderTrigger
    
    CreateElement --> UserAction
    TriggerRerender --> RenderTrigger
    
    InitMatrix --> StartTracking
    ProcessModeData --> StartTracking
    CreateElement --> EndTracking

    %% 样式
    classDef dataSource fill:#e1f5fe
    classDef stateLayer fill:#f3e5f5
    classDef coreEngine fill:#fff3e0
    classDef componentLayer fill:#e8f5e8
    classDef initFlow fill:#fff8e1
    classDef modeSwitch fill:#fce4ec
    classDef renderFlow fill:#e0f2f1
    classDef interactionFlow fill:#f1f8e9
    classDef performance fill:#fafafa

    class GroupAData,OptimizedData,DataUtils dataSource
    class ZustandStore,MatrixState,StoreActions stateLayer
    class MatrixCore,ModeHandlers,ProcessData,RenderCell,HandleInteraction coreEngine
    class MatrixComponent,MatrixHooks,RenderLogic componentLayer
    class Start,CheckClient,CheckInit,InitMatrix,CreateCells,LoadData,SetCellData,CacheUpdate initFlow
    class TriggerMode,UpdateConfig,InvalidateCache,SelectHandler,ProcessModeData,GenerateRenderData modeSwitch
    class RenderTrigger,GetRenderData,ComputeStyle,ComputeContent,ComputeClassName,CreateElement renderFlow
    class UserAction,ParseCoordinate,UpdateState,TriggerRerender interactionFlow
    class StartTracking,Metrics,EndTracking performance
            </div>
        </div>
        
        <h3>🔍 架构层次说明</h3>
        <ul>
            <li><strong>数据源层</strong>：提供基础数据结构和预处理数据集</li>
            <li><strong>状态管理层</strong>：基于Zustand的响应式状态管理，支持持久化</li>
            <li><strong>核心引擎层</strong>：MatrixCore类和业务模式处理器</li>
            <li><strong>组件层</strong>：React组件和渲染逻辑</li>
            <li><strong>业务流程</strong>：初始化、模式切换、渲染、交互、性能监控</li>
        </ul>
    </section>

    </section>

    <section id="sequence" class="section">
        <h2>⏱️ 函数调用序列图</h2>
        <p>以下序列图详细展示了矩阵系统中各个函数之间的调用关系和时序，包括初始化、渲染、模式切换、交互和性能监控的完整流程。</p>

        <div class="mermaid-container">
            <div class="mermaid">
sequenceDiagram
    participant User as 👤 用户
    participant Component as 🎨 Matrix组件
    participant Store as 🏪 MatrixStore
    participant Core as ⚙️ MatrixCore
    participant Data as 🗄️ GroupAData
    participant Handler as 🔧 ModeHandler

    %% 初始化序列
    Note over User,Handler: 🚀 矩阵初始化流程
    User->>Component: 访问页面
    Component->>Component: useEffect() 触发
    Component->>Store: initializeMatrix()
    Store->>Store: set(produce((state) => {...}))
    Store->>Data: 访问 OPTIMIZED_GROUP_A_DATA
    Data-->>Store: 返回预处理数据集

    loop 33x33 单元格
        Store->>Store: createDefaultCell(x, y)
        Store->>Data: getMatrixDataByCoordinate(x, y)
        Data-->>Store: 返回 MatrixDataPoint | null
        Store->>Store: 设置 cell.color, cell.level, cell.value
    end

    Store->>Store: 更新 state.data.cells
    Store->>Store: invalidateCache()
    Store-->>Component: 状态更新完成

    %% 渲染序列
    Note over User,Handler: 🎯 渲染流程
    Component->>Component: renderMatrixCells()

    loop 每个单元格 (x, y)
        Component->>Store: getCellRenderData(x, y)
        Store->>Core: matrixCore.renderCell(cell, config)
        Core->>Core: 获取 modeHandlers[config.mode]
        Core->>Handler: handler.renderCell(cell, config)

        alt coordinate 模式
            Handler->>Handler: 计算坐标内容 "${x},${y}"
            Handler->>Handler: 设置坐标样式
        else color 模式
            Handler->>Handler: 计算颜色内容和背景
            Handler->>Handler: 设置颜色样式
        else value 模式
            Handler->>Handler: 计算数值内容
            Handler->>Handler: 设置数值样式
        else word 模式
            Handler->>Handler: 计算文字内容
            Handler->>Handler: 设置文字样式
        end

        Handler-->>Core: 返回 CellRenderData
        Core-->>Store: 返回渲染数据
        Store-->>Component: 返回单元格渲染数据
        Component->>Component: 创建 DOM 元素
    end

    %% 模式切换序列
    Note over User,Handler: 🔄 模式切换流程
    User->>Component: 切换显示模式
    Component->>Store: setMode(newMode)
    Store->>Store: set(produce((state) => { state.config.mode = newMode }))
    Store->>Store: invalidateCache()
    Store->>Store: 触发状态更新
    Store-->>Component: 配置更新完成
    Component->>Component: 重新渲染 (useEffect 依赖变化)

    %% 重新渲染使用新模式
    Component->>Store: getCellRenderData(x, y)
    Store->>Core: matrixCore.renderCell(cell, newConfig)
    Core->>Core: 获取 modeHandlers[newConfig.mode]
    Core->>Handler: 新模式处理器.renderCell(cell, newConfig)
    Handler-->>Core: 返回新模式的渲染数据
    Core-->>Store: 返回新渲染数据
    Store-->>Component: 返回更新的渲染数据
    Component->>Component: 更新 DOM 元素

    %% 交互序列
    Note over User,Handler: 🖱️ 用户交互流程
    User->>Component: 点击单元格
    Component->>Component: handleCellClick(event)
    Component->>Component: 解析坐标 data-x, data-y
    Component->>Store: selectCell(x, y, multiSelect)
    Store->>Store: set(produce((state) => {...}))
    Store->>Store: 更新 selectedCells, cell.isSelected
    Store->>Store: 清除缓存
    Store-->>Component: 选择状态更新
    Component->>Component: 重新渲染受影响的单元格

    %% 性能监控序列
    Note over User,Handler: 📊 性能监控流程
    Store->>Store: startPerformanceTracking()
    Store->>Store: const startTime = performance.now()
    Store->>Core: 执行核心操作
    Core->>Handler: 执行处理器操作
    Handler-->>Core: 操作完成
    Core-->>Store: 操作完成
    Store->>Store: endPerformanceTracking(operation)
    Store->>Store: const endTime = performance.now()
    Store->>Store: 更新 metrics.renderTime
    Store->>Store: 性能警告检查 (> 100ms)
            </div>
        </div>

        <h3>🔍 序列流程说明</h3>
        <ul>
            <li><strong>初始化序列</strong>：从组件挂载到矩阵数据加载完成的完整流程</li>
            <li><strong>渲染序列</strong>：每个单元格的渲染过程和模式处理器调用</li>
            <li><strong>模式切换序列</strong>：配置更新和重新渲染的完整流程</li>
            <li><strong>交互序列</strong>：用户点击到状态更新的完整链路</li>
            <li><strong>性能监控序列</strong>：性能数据的收集和分析</li>
        </ul>
    </section>

    <section id="data-structure" class="section">
        <h2>📊 状态和数据结构图</h2>
        <p>以下图表展示了矩阵系统中所有重要的数据结构和它们之间的关系，包括核心数据结构、渲染数据结构、业务模式处理器、状态管理、缓存系统等。</p>

        <div class="mermaid-container">
            <div class="mermaid">
graph LR
    %% 核心数据结构
    subgraph DataStructures["📊 核心数据结构"]
        MatrixData["MatrixData<br/>• cells: Map&lt;string, CellData&gt;<br/>• selectedCells: Set&lt;string&gt;<br/>• hoveredCell: string | null<br/>• focusedCell: string | null"]

        CellData["CellData<br/>• x: number<br/>• y: number<br/>• color?: BasicColorType<br/>• level?: DataLevel<br/>• value?: number<br/>• word?: string<br/>• isActive: boolean<br/>• isSelected: boolean<br/>• isHovered: boolean"]

        MatrixConfig["MatrixConfig<br/>• mode: BusinessMode<br/>  - 'coordinate'<br/>  - 'color'<br/>  - 'value'<br/>  - 'word'"]

        MatrixData --> CellData
    end

    %% 渲染数据结构
    subgraph RenderStructures["🎨 渲染数据结构"]
        CellRenderData["CellRenderData<br/>• content: string<br/>• style: CellStyle<br/>• className: string<br/>• isInteractive: boolean"]

        CellStyle["CellStyle<br/>• backgroundColor: string<br/>• border: string<br/>• color: string<br/>• fontSize: string<br/>• fontWeight?: string"]

        ProcessedMatrixData["ProcessedMatrixData<br/>• cells: Map&lt;string, CellData&gt;<br/>• renderData: Map&lt;string, CellRenderData&gt;<br/>• metadata: RenderMetadata"]

        CellRenderData --> CellStyle
        ProcessedMatrixData --> CellRenderData
    end

    %% 业务模式处理器
    subgraph ModeHandlers["🔧 业务模式处理器"]
        CoordinateMode["coordinateModeHandler<br/>• content: '${x},${y}'<br/>• backgroundColor: selected ? '#e3f2fd' : '#ffffff'<br/>• className: 'coordinate-mode'"]

        ColorMode["colorModeHandler<br/>• content: color name<br/>• backgroundColor: color hex<br/>• className: 'color-mode'"]

        LevelMode["levelModeHandler<br/>• content: level.toString()<br/>• backgroundColor: selected ? '#fff3cd' : '#ffffff'<br/>• className: 'level-mode'"]

        WordMode["wordModeHandler<br/>• content: word || ''<br/>• backgroundColor: selected ? '#e8f5e8' : '#ffffff'<br/>• className: 'word-mode'"]
    end

    %% 状态管理
    subgraph StateManagement["🏪 状态管理"]
        ZustandStore["useMatrixStore<br/>(Zustand + Persist)"]

        StoreState["MatrixStoreState<br/>• data: MatrixData<br/>• config: MatrixConfig<br/>• matrixData: MatrixDataSet<br/>• cache: ComputedCache<br/>• metrics: PerformanceMetrics<br/>• isLoading: boolean<br/>• isDirty: boolean<br/>• lastUpdate: number"]

        StoreActions["MatrixStoreActions<br/>• initializeMatrix()<br/>• updateCell()<br/>• updateCells()<br/>• clearMatrix()<br/>• setMode()<br/>• selectCell()<br/>• selectCells()<br/>• clearSelection()<br/>• hoverCell()<br/>• focusCell()<br/>• getCellRenderData()"]

        ZustandStore --> StoreState
        ZustandStore --> StoreActions
    end

    %% 缓存系统
    subgraph CacheSystem["💾 缓存系统"]
        ComputedCache["ComputedCache<br/>• cellStyles: Map&lt;string, CellStyle&gt;<br/>• cellContents: Map&lt;string, string&gt;<br/>• cellClassNames: Map&lt;string, string&gt;<br/>• interactionStates: Map&lt;string, boolean&gt;<br/>• lastUpdate: number"]

        CacheOperations["缓存操作<br/>• invalidateCache()<br/>• updateCache()<br/>• computeCellContent()<br/>• computeCellStyle()<br/>• computeCellClassName()"]

        ComputedCache --> CacheOperations
    end

    %% 性能监控
    subgraph PerformanceMonitoring["📊 性能监控"]
        PerformanceMetrics["PerformanceMetrics<br/>• renderTime: number<br/>• updateTime: number<br/>• cacheHitRate: number<br/>• memoryUsage: number<br/>• frameRate: number"]

        PerformanceOperations["性能操作<br/>• startPerformanceTracking()<br/>• endPerformanceTracking()<br/>• performance.now()<br/>• 性能警告 (> 100ms)"]

        PerformanceMetrics --> PerformanceOperations
    end

    %% 数据源
    subgraph DataSource["🗄️ 数据源"]
        GroupAData["GROUP_A_DATA<br/>基础数据结构<br/>• black: { 1: [[0,0]] }<br/>• red: { 1,2,3,4: coordinates }<br/>• orange, yellow, green...<br/>• cyan, blue, purple, pink"]

        OptimizedData["OPTIMIZED_GROUP_A_DATA<br/>预处理数据集<br/>• points: MatrixDataPoint[]<br/>• byColor: Map<br/>• byLevel: Map<br/>• byGroup: Map<br/>• byCoordinate: Map<br/>• metadata: 统计信息"]

        MatrixDataPoint["MatrixDataPoint<br/>• x: number<br/>• y: number<br/>• color: BasicColorType<br/>• level: DataLevel<br/>• group: GroupType<br/>• id: string"]

        GroupAData --> OptimizedData
        OptimizedData --> MatrixDataPoint
    end

    %% 工具函数
    subgraph UtilityFunctions["🔧 工具函数"]
        CoordinateUtils["坐标工具<br/>• coordinateKey(x, y)<br/>• parseCoordinateKey(key)<br/>• toAbsoluteCoordinate()<br/>• applyOffset()<br/>• isValidCoordinate()"]

        DataUtils["数据工具<br/>• createDefaultCell()<br/>• getMatrixDataByCoordinate()<br/>• hasMatrixData()<br/>• generateGroupData()"]

        TypeUtils["类型工具<br/>• BasicColorType<br/>• DataLevel<br/>• BusinessMode<br/>• GroupType"]
    end

    %% 连接关系
    MatrixConfig --> ModeHandlers
    StoreState --> MatrixData
    StoreState --> MatrixConfig
    StoreState --> ComputedCache
    StoreState --> PerformanceMetrics
    StoreState --> OptimizedData

    StoreActions --> CoordinateUtils
    StoreActions --> DataUtils

    ModeHandlers --> ProcessedMatrixData
    ProcessedMatrixData --> CacheSystem

    OptimizedData --> StoreActions

    %% 样式定义
    classDef dataStructure fill:#e3f2fd
    classDef renderStructure fill:#f3e5f5
    classDef modeHandler fill:#fff3e0
    classDef stateManagement fill:#e8f5e8
    classDef cacheSystem fill:#fff8e1
    classDef performance fill:#fce4ec
    classDef dataSource fill:#e0f2f1
    classDef utility fill:#f1f8e9

    class MatrixData,CellData,MatrixConfig dataStructure
    class CellRenderData,CellStyle,ProcessedMatrixData renderStructure
    class CoordinateMode,ColorMode,ValueMode,WordMode modeHandler
    class ZustandStore,StoreState,StoreActions stateManagement
    class ComputedCache,CacheOperations cacheSystem
    class PerformanceMetrics,PerformanceOperations performance
    class GroupAData,OptimizedData,MatrixDataPoint dataSource
    class CoordinateUtils,DataUtils,TypeUtils utility
            </div>
        </div>

        <h3>🔍 数据结构说明</h3>
        <ul>
            <li><strong>核心数据结构</strong>：MatrixData、CellData、MatrixConfig等基础数据类型</li>
            <li><strong>渲染数据结构</strong>：CellRenderData、CellStyle等渲染相关的数据类型</li>
            <li><strong>业务模式处理器</strong>：四种显示模式的具体实现逻辑</li>
            <li><strong>状态管理</strong>：基于Zustand的状态存储和操作方法</li>
            <li><strong>缓存系统</strong>：计算属性缓存和缓存管理操作</li>
            <li><strong>性能监控</strong>：性能指标收集和监控操作</li>
            <li><strong>数据源</strong>：从基础数据到预处理数据集的完整数据链</li>
            <li><strong>工具函数</strong>：坐标、数据和类型相关的工具函数</li>
        </ul>
    </section>

    </section>

    <section id="key-features" class="section">
        <h2>🚀 关键特性</h2>
        <p>矩阵格子系统具备以下核心特性，确保了高性能、可维护性和可扩展性：</p>

        <div class="features-grid">
            <div class="feature-card">
                <h4>🗄️ 数据驱动架构</h4>
                <p>基于OPTIMIZED_GROUP_A_DATA的33x33矩阵数据，支持9种颜色（black、red、cyan、yellow、purple、orange、green、blue、pink）和4个层级的完整数据体系。</p>
            </div>

            <div class="feature-card">
                <h4>🔄 多模式切换</h4>
                <p>支持coordinate（坐标）、color（颜色）、value（数值）、word（文字）四种显示模式的无缝切换，每种模式都有独立的渲染逻辑和样式配置。</p>
            </div>

            <div class="feature-card">
                <h4>⚡ 高性能渲染</h4>
                <p>使用Map/Set数据结构优化查询性能，计算属性缓存减少重复计算，确保1089个单元格（33x33）的流畅渲染，初始化时间控制在100ms以内。</p>
            </div>

            <div class="feature-card">
                <h4>🏪 响应式状态管理</h4>
                <p>基于Zustand的状态管理系统，支持状态持久化、Immer不可变更新、计算属性缓存，确保状态变更的高效响应。</p>
            </div>

            <div class="feature-card">
                <h4>🎨 模块化渲染引擎</h4>
                <p>MatrixCore核心引擎配合业务模式处理器，实现了可扩展的渲染架构，每种模式都有独立的processData、renderCell和handleInteraction方法。</p>
            </div>

            <div class="feature-card">
                <h4>📊 完整性能监控</h4>
                <p>内置性能监控系统，实时收集renderTime、updateTime、cacheHitRate、memoryUsage等关键指标，并提供性能警告机制。</p>
            </div>

            <div class="feature-card">
                <h4>🖱️ 丰富交互支持</h4>
                <p>支持click、hover、focus等多种交互事件，提供selectCell、hoverCell、focusCell等状态管理方法，支持单选和多选操作。</p>
            </div>

            <div class="feature-card">
                <h4>💾 智能缓存系统</h4>
                <p>计算属性缓存系统包含cellStyles、cellContents、cellClassNames、interactionStates等缓存，支持缓存失效和更新机制。</p>
            </div>
        </div>
    </section>

    <section id="summary" class="section">
        <h2>📋 总结</h2>

        <div class="summary-box">
            <h3>🎯 系统架构优势</h3>
            <ul class="summary-list">
                <li><strong>分层架构清晰</strong>：数据源层、状态管理层、核心引擎层、组件层职责明确</li>
                <li><strong>数据流向明确</strong>：从GROUP_A_DATA到最终DOM渲染的完整数据流</li>
                <li><strong>模式切换灵活</strong>：支持四种显示模式的无缝切换</li>
                <li><strong>性能优化到位</strong>：Map/Set数据结构、计算属性缓存、性能监控</li>
                <li><strong>交互体验良好</strong>：支持多种交互事件和状态管理</li>
                <li><strong>可扩展性强</strong>：模块化的业务模式处理器架构</li>
            </ul>
        </div>

        <h3>🔧 技术实现亮点</h3>
        <ul>
            <li><strong>预处理数据集</strong>：OPTIMIZED_GROUP_A_DATA提供了高效的数据查询能力</li>
            <li><strong>Zustand状态管理</strong>：轻量级、高性能的状态管理解决方案</li>
            <li><strong>Immer不可变更新</strong>：确保状态更新的安全性和可预测性</li>
            <li><strong>计算属性缓存</strong>：减少重复计算，提升渲染性能</li>
            <li><strong>业务模式处理器</strong>：可扩展的模式切换架构</li>
            <li><strong>性能监控机制</strong>：实时监控和性能警告</li>
        </ul>

        <h3>📈 性能指标</h3>
        <ul>
            <li><strong>矩阵规模</strong>：33x33 = 1089个单元格</li>
            <li><strong>初始化时间</strong>：< 100ms（性能警告阈值）</li>
            <li><strong>数据结构</strong>：Map/Set优化查询性能</li>
            <li><strong>缓存机制</strong>：计算属性缓存减少重复计算</li>
            <li><strong>内存管理</strong>：实时监控内存使用情况</li>
        </ul>

        <h3>🔮 扩展能力</h3>
        <ul>
            <li><strong>新增显示模式</strong>：通过添加新的ModeHandler轻松扩展</li>
            <li><strong>数据源扩展</strong>：支持A-M组数据的完整扩展</li>
            <li><strong>交互功能扩展</strong>：可添加更多交互事件和状态</li>
            <li><strong>性能优化扩展</strong>：可添加更多性能监控指标</li>
            <li><strong>UI组件扩展</strong>：可基于现有架构开发更多UI组件</li>
        </ul>
    </section>

    <div class="timestamp">
        <p>📅 文档生成时间：2025年1月30日</p>
        <p>🏢 Cube1 Group 项目 - 矩阵系统架构分析报告</p>
        <p>📧 如有疑问，请联系开发团队</p>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            sequence: {
                useMaxWidth: true,
                wrap: true
            }
        });
    </script>
</body>
</html>
